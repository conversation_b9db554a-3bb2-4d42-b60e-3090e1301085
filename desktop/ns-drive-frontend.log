[FRONTEND] 2025/07/04 23:34:50 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751646889624_0cenqzbso | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:34:50 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646889638_h6xzawquk | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:35:04 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646904191_tzsyhmcif | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:35:09 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646906008_jhfwps5yc | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:35:19 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646916376_kd4x4m56d | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:36:39 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646996599_pnyhia3wt | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:37:36 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647055112_c4kh7wx3r | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:38:40 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647118740_43xtp8dwr | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:26 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647221461_giazzxyfu | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:26 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647221469_77mbruapb | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:38 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647234735_o2mik9vs1 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:38 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647234744_rkymzzwee | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:29 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647644397_1codds3vr | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:29 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647644415_i2bcqoqzi | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:29 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647646299_cxu7upefx | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:29 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647647658_zd41g50je | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:49 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647665074_zps0otjzm | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:54 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647670014_sta12hv1w | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:47:54 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647672904_c5q6qgyya | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:50:14 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647812917_czx1dty4k | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:50:14 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647812927_fxn91m2er | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:50:50 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647845996_q6boyhbev | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:50:50 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647846041_q4l64veh7 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:51:15 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647873333_szixwx33i | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:51:55 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647911566_kfo1gsv1d | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:51:55 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647912950_he5p2ms0r | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:51:55 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647914278_huu8fry7j | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:01:30 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:51770/ | TraceID: trace_1751648488421_x6eedb4ad | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:51770/","referrer":""}
[FRONTEND] 2025/07/05 00:01:30 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:51770/ | TraceID: trace_1751648488444_jeujoe6lb | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:51770/","referrer":""}
[FRONTEND] 2025/07/05 00:07:12 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751648831129_974j2rn5l | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:07:12 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751648831144_rq2u7ys94 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:08:28 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751648903476_z9r1oeijs | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:08:28 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751648903491_jcn6o4pgu | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:08:28 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751648904695_ek6nagwml | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:10:34 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649031235_8qw52g3iq | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:13:59 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649234802_m94r6movy | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:13:59 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649234807_wm73gagvn | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:17:24 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649439053_dfv1ji3vj | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:17:24 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649439057_v6burcl7x | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:17:48 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649463511_9mpr8rh0z | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:17:48 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649463515_6fvljdwy2 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:20:01 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649596706_7bkrb3i3e | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:20:01 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649596710_jxbr5vfsc | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:20:54 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649649136_0t25qqfic | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:20:54 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649649140_094w4efdt | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:21:05 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649660717_ispnvb5te | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:21:05 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649660724_dpuyu1mi0 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:21:21 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649676161_zodyh7ja0 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:21:21 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649676173_ui9bne1hw | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:21:38 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649693037_hhwgdr7lz | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:21:38 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649693043_qtxqqxdq1 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:22:33 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649748910_fd7g1qrmh | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:22:33 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649748917_tee9py4y1 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:23:38 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649813625_byoiuj07s | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:23:38 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649813629_pkjei6anw | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:24:29 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649864202_cbged0lke | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:24:29 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649864209_zemtzicut | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:25:04 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751649899077_91rzmpc3a | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:25:04 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751649899081_9dgw20vjx | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:29:02 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751650137527_m0aze1c6g | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:29:02 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751650137533_c2po9mnj7 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:29:15 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751650149937_nymq063f0 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:29:15 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751650149941_kwfr65d1x | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:29:27 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751650162076_g1qitt6m5 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:29:27 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751650162083_49ncn3cwq | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:30:00 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751650195232_6l7mqg717 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:30:00 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751650195238_peshcqecg | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:30:25 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751650220007_qr59016na | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:30:25 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751650220013_xhmuxnf1z | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:30:39 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751650234045_zm6hx73ak | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:30:39 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751650234053_j05sgljgq | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:46:20 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651174945_0oiy0mtp9 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:46:20 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651174954_kc65b2l2y | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:46:53 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651208482_446soogyn | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:46:53 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651208498_gbxb2fils | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:55:27 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651722887_7z9i5q5h3 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:55:27 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651722894_7l9ylfzok | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:55:57 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651757165_rmcotbe2s | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:56:02 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651759067_fc9nw8iat | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:56:02 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651760059_mtu3cnjw5 | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:56:02 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651761814_umvmmrbsy | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:56:07 Level: ERROR | Message: Error exporting remotes: Error: Error calling method: ExportRemotes expects 0 arguments, received 1 | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651763506_ssrb6zmpg | Details: [
  "Error exporting remotes:",
  {
    "name": "Error",
    "message": "Error calling method: ExportRemotes expects 0 arguments, received 1",
    "stack": "errorHandler@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:1285:31\nglobal code@wails://localhost:9245/:1:24"
  }
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
@wails://localhost:9245/main.js:1976:22
rejected@wails://localhost:9245/main.js:35:29
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:07 Level: ERROR | Message: API Error: Error: Error calling method: ExportRemotes expects 0 arguments, received 1 Context: export_remotes | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651763506_ezxrcblpv | Details: [
  "API Error:",
  {
    "name": "Error",
    "message": "Error calling method: ExportRemotes expects 0 arguments, received 1",
    "stack": "errorHandler@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:1285:31\nglobal code@wails://localhost:9245/:1:24"
  },
  "Context:",
  "export_remotes"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
handleApiError@wails://localhost:9245/main.js:727:18
@wails://localhost:9245/main.js:1977:41
rejected@wails://localhost:9245/main.js:35:29
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:07 Level: ERROR | Message: API Error: Error calling method: ExportRemotes expects 0 arguments, received 1 | Context: export_remotes | Component: export_remotes | URL: wails://localhost:9245/ | TraceID: trace_1751651763506_nljc3lyn1 | Details: {} | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: errorHandler@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:1285:31
global code@wails://localhost:9245/:1:24
[FRONTEND] 2025/07/05 00:56:07 Level: ERROR | Message: Error exporting remotes: Error: Failed to export remotes | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651763508_mlsa4d3i3 | Details: [
  "Error exporting remotes:",
  {
    "name": "Error",
    "message": "Failed to export remotes",
    "stack": "@wails://localhost:9245/main.js:1978:24\nrejected@wails://localhost:9245/main.js:35:29\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29\nonInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31\nrun@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35\nrunTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47\ndrainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30"
  }
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
@wails://localhost:9245/main.js:6006:22
rejected@wails://localhost:9245/main.js:35:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:07 Level: ERROR | Message: API Error: Error: Failed to export remotes Context: export_remotes | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651763508_5qpa4t8y0 | Details: [
  "API Error:",
  {
    "name": "Error",
    "message": "Failed to export remotes",
    "stack": "@wails://localhost:9245/main.js:1978:24\nrejected@wails://localhost:9245/main.js:35:29\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29\nonInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31\nrun@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35\nrunTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47\ndrainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30"
  },
  "Context:",
  "export_remotes"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
handleApiError@wails://localhost:9245/main.js:727:18
@wails://localhost:9245/main.js:6007:41
rejected@wails://localhost:9245/main.js:35:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:07 Level: ERROR | Message: API Error: Failed to export remotes | Context: export_remotes | Component: export_remotes | URL: wails://localhost:9245/ | TraceID: trace_1751651763509_fieitpls5 | Details: {} | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: @wails://localhost:9245/main.js:1978:24
rejected@wails://localhost:9245/main.js:35:29
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:07 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651767816_95opb8ygo | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:56:12 Level: ERROR | Message: Error exporting profiles: Error: Error calling method: ExportProfiles expects 0 arguments, received 1 | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651770061_yc7rwdp9f | Details: [
  "Error exporting profiles:",
  {
    "name": "Error",
    "message": "Error calling method: ExportProfiles expects 0 arguments, received 1",
    "stack": "errorHandler@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:1285:31\nglobal code@wails://localhost:9245/:1:24"
  }
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
@wails://localhost:9245/main.js:1953:22
rejected@wails://localhost:9245/main.js:35:29
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:12 Level: ERROR | Message: API Error: Error: Error calling method: ExportProfiles expects 0 arguments, received 1 Context: export_profiles | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651770061_8l8r47ryr | Details: [
  "API Error:",
  {
    "name": "Error",
    "message": "Error calling method: ExportProfiles expects 0 arguments, received 1",
    "stack": "errorHandler@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:1285:31\nglobal code@wails://localhost:9245/:1:24"
  },
  "Context:",
  "export_profiles"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
handleApiError@wails://localhost:9245/main.js:727:18
@wails://localhost:9245/main.js:1954:41
rejected@wails://localhost:9245/main.js:35:29
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:12 Level: ERROR | Message: API Error: Error calling method: ExportProfiles expects 0 arguments, received 1 | Context: export_profiles | Component: export_profiles | URL: wails://localhost:9245/ | TraceID: trace_1751651770061_713k7j52z | Details: {} | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: errorHandler@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:1285:31
global code@wails://localhost:9245/:1:24
[FRONTEND] 2025/07/05 00:56:12 Level: ERROR | Message: Error exporting profiles: Error: Failed to export profiles | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651770062_qq3cs8qow | Details: [
  "Error exporting profiles:",
  {
    "name": "Error",
    "message": "Failed to export profiles",
    "stack": "@wails://localhost:9245/main.js:1955:24\nrejected@wails://localhost:9245/main.js:35:29\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29\nonInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31\nrun@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35\nrunTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47\ndrainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30"
  }
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
@wails://localhost:9245/main.js:3996:22
rejected@wails://localhost:9245/main.js:35:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:12 Level: ERROR | Message: API Error: Error: Failed to export profiles Context: export_profiles | Context: console.error | Component: console.error | URL: wails://localhost:9245/ | TraceID: trace_1751651770062_by0x26p55 | Details: [
  "API Error:",
  {
    "name": "Error",
    "message": "Failed to export profiles",
    "stack": "@wails://localhost:9245/main.js:1955:24\nrejected@wails://localhost:9245/main.js:35:29\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29\nonInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31\nrun@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41\n@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32\nonInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35\nrunTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47\ndrainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30"
  },
  "Context:",
  "export_profiles"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: createLogEntry@wails://localhost:9245/main.js:539:29
error@wails://localhost:9245/main.js:659:40
logToBackend@wails://localhost:9245/main.js:6654:36
@wails://localhost:9245/main.js:6617:24
handleApiError@wails://localhost:9245/main.js:727:18
@wails://localhost:9245/main.js:3997:41
rejected@wails://localhost:9245/main.js:35:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:56:12 Level: ERROR | Message: API Error: Failed to export profiles | Context: export_profiles | Component: export_profiles | URL: wails://localhost:9245/ | TraceID: trace_1751651770062_y9d9hxpi8 | Details: {} | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""} | StackTrace: @wails://localhost:9245/main.js:1955:24
rejected@wails://localhost:9245/main.js:35:29
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/@wailsio_runtime.js:847:29
onInvoke@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13238:31
run@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:103:41
@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:2016:33
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13025:32
onInvokeTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/chunk-3O5UWTH4.js:13227:35
runTask@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:141:47
drainMicroTaskQueue@wails://localhost:9245/@fs/Users/<USER>/Github/ns-drive/desktop/frontend/.angular/cache/20.0.5/desktop/vite/deps/zone__js.js:520:30
[FRONTEND] 2025/07/05 00:58:32 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651910391_wvtbt5p2i | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:58:32 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651910410_viyz3g77s | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:58:36 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651915939_u7mt9ij59 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:58:36 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651915949_yyopaq0rt | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:07 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651942314_7m53ogf9t | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:07 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651942323_it565kzi2 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:32 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651967465_jln63w65v | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:32 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651967478_yzlqbvlqm | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:34 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751651972985_mjihcnd0j | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:34 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651972993_cd3e4wgdt | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:48 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651987130_p037lwcik | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:52 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651988184_v31us7xpl | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 00:59:52 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751651989546_4l39u06hc | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:00:44 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751652039210_6hbwmiph2 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:00:44 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751652039220_i3fhtsc3z | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:11:16 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751652674386_8mgvzfq64 | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:13:28 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751652803678_ldjgsnsug | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:13:28 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751652803690_sm1x1iauu | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:15:36 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751652935382_2cfgzfp3b | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:15:36 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751652935391_dcqrv29sy | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:15:45 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751652944718_h7ygcm1na | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:15:45 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751652944729_nm21n9rj5 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:16:14 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751652969396_3emmjccf0 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:16:14 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751652969403_sch4ulkt0 | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:16:45 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751653000528_cgr4fzb99 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:16:45 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751653000533_0m7bd4wut | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:18:08 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751653088160_bs5m3dk7a | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:18:08 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751653088165_r3ddbkeko | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:22:53 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 2. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751653371132_yrcc8l62z | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 2. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:25:30 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751653525032_vcv1a459b | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:25:30 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751653525036_2dp904dxm | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:25:42 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751653537842_f6j5rbiy1 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/05 01:25:42 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751653537846_m9jjunc0d | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
